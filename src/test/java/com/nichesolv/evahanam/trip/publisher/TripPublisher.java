package com.nichesolv.evahanam.trip.publisher;

import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.trip.listener.TripListener;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import com.nichesolv.evahanam.vehicle.enums.VehicleState;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@Slf4j
@SpringBootTest
public class TripPublisher {

    @SpyBean
    TripListener tripListener;

    @SpyBean
    TripSummaryService tripSummaryService;

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    ApplicationEventPublisher eventPublisher;

    @Test
    @Transactional
    @Rollback
    void testEventDurationStartLimitReached() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        int tripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        assertThat(tripSize).isEqualTo(0);
        log.info("Trip size before event: {}", tripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(60L);
        monitor.setStoppageTime(0L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(Instant.now());
        monitor.setStoppageTimeLastUpdatedOn(null);
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        verify(tripListener, times(1)).onApplicationEvent(event);
    }

    @Test
    @Transactional
    @Rollback
    void testEventDurationStartLimitNotReached() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        int tripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        assertThat(tripSize).isEqualTo(0);
        log.info("Trip size before event: {}", tripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(50L);
        monitor.setStoppageTime(0L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(Instant.now());
        monitor.setStoppageTimeLastUpdatedOn(null);
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        verify(tripListener, times(1)).onApplicationEvent(event);
        int tripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        assertThat(tripSizeNew).isEqualTo(tripSize);
    }

    @Test
    @Transactional
    @Rollback
    void testIncorrectEventPublish() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        int tripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        assertThat(tripSize).isEqualTo(0);
        log.info("Trip size before event: {}", tripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(50L);
        monitor.setStoppageTime(0L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(Instant.now());
        monitor.setStoppageTimeLastUpdatedOn(null);
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        status.setVehicleState(VehicleState.CHARGING);
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        verify(tripListener, times(1)).onApplicationEvent(event);
        int tripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        assertThat(tripSizeNew).isEqualTo(tripSize);
    }

    @Test
    @Transactional
    @Rollback
    void testEventDurationEndLimitReached() {
        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        testEventDurationStartLimitReached();
        int inProgressTripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        int completedTripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC).size();
        assertThat(inProgressTripSize).isEqualTo(1);
        log.info("trip stats before event publish: inProgress:{} ,completed:{}", inProgressTripSize,completedTripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(603L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(Instant.now());
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        verify(tripListener,times(1)). onApplicationEvent(event);
        int inProgressTripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        int completedTripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC).size();
        log.info("trip stats after event publish: inProgress:{} ,completed:{}", inProgressTripSizeNew,completedTripSizeNew);
        assertThat(
                completedTripSizeNew
        ).isGreaterThan(completedTripSize);

        assertThat(
                inProgressTripSize
        ).isGreaterThan(inProgressTripSizeNew);

    }

    @Test
    @Transactional
    @Rollback
    void testEventDurationEndLimitNotReached() {
        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        testEventDurationStartLimitReached();
        int inProgressTripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        int completedTripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC).size();
        assertThat(inProgressTripSize).isEqualTo(1);
        log.info("trip stats before event publish: inProgress:{} ,completed:{}", inProgressTripSize,completedTripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(500L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(Instant.now());
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        eventPublisher.publishEvent(event);
        verify(tripListener,times(1)). onApplicationEvent(event);
        int inProgressTripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        int completedTripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC).size();
        log.info("trip stats after event publish: inProgress:{} ,completed:{}", inProgressTripSizeNew,completedTripSizeNew);
        assertThat(
                completedTripSizeNew
        ).isEqualTo(completedTripSize);

        assertThat(
                inProgressTripSize
        ).isEqualTo(inProgressTripSizeNew);

    }
}
