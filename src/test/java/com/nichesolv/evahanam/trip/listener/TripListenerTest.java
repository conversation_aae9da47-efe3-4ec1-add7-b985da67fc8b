package com.nichesolv.evahanam.trip.listener;

import com.nichesolv.evahanam.telemetryData.service.vehicleStatus.VehicleStatusService;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatusIdx;
import com.nichesolv.evahanam.vehicle.repository.VehicleStatusRepository;
import com.nichesolv.evahanam.vehicle.service.IVehicleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.test.context.SpringBootTest;
import com.nichesolv.evahanam.common.enums.EventType;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.trip.enums.TestRideSummaryPopulationStatus;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.evahanam.trip.enums.TripType;
import com.nichesolv.evahanam.trip.jpa.EventDurationConstants;
import com.nichesolv.evahanam.trip.jpa.Trip;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.trip.repository.EventDurationConstantsRepository;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.context.ApplicationEventPublisher;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.in;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@Slf4j
@SpringBootTest
public class TripListenerTest {


    @SpyBean
    TripListener tripListener;

    @SpyBean
    TripSummaryService tripSummaryService;

    @Autowired
    IVehicleService vehicleService;

    @Autowired
    VehicleStatusRepository vehicleStatusRepository;

    @Autowired
    ApplicationEventPublisher eventPublisher;



    @Test
    @Transactional
    @Rollback
    void testTripCreation() {


        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        int tripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        assertThat(tripSize).isEqualTo(0);
        log.info("Trip size before event: {}", tripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(60L);
        monitor.setStoppageTime(0L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(Instant.now());
        monitor.setStoppageTimeLastUpdatedOn(null);
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        tripListener.onApplicationEvent(event);
        int tripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        log.info("Trip size after event publish: {}", tripSizeNew);
        assertThat(
                tripSizeNew
        ).isGreaterThan(tripSize);
    }



    @Test
    @Transactional
    @Rollback
    void testTripCompletion() {
        Vehicle vehicle = vehicleService.getVehicleByAnyId("861100065561520");
        testTripCreation();
        int inProgressTripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        int completedTripSize = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC).size();
        assertThat(inProgressTripSize).isEqualTo(1);
        log.info("trip stats before event publish: inProgress:{} ,completed:{}", inProgressTripSize,completedTripSize);
        VehicleEventMonitor monitor = new VehicleEventMonitor();
        monitor.setImei(vehicle.getImei());
        monitor.setVehicle(vehicle);
        monitor.setRunningTime(0L);
        monitor.setStoppageTime(603L);
        monitor.setChargingTime(0l);
        monitor.setRunningTimeLastUpdatedOn(null);
        monitor.setStoppageTimeLastUpdatedOn(Instant.now());
        monitor.setChargingTimeLastUpdatedOn(null);


        VehicleStatus status = vehicleStatusRepository.findByVehicleStatusIdxImeiAndVehicleStatusIdxTimestampBetweenOrderByVehicleStatusIdxTimestampAsc(vehicle.getImei(),Instant.now().minus(20 , ChronoUnit.DAYS),Instant.now()).findFirst().get();
        VehicleStateEvent event = new VehicleStateEvent(monitor, status);


        tripListener.onApplicationEvent(event);
        int inProgressTripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.IN_PROGRESS, TripType.AUTOMATIC).size();
        int completedTripSizeNew = tripSummaryService.findByImeiAndStatusAndTripType(vehicle, TestRideSummaryPopulationStatus.COMPLETED, TripType.AUTOMATIC).size();
        log.info("trip stats after event publish: inProgress:{} ,completed:{}", inProgressTripSizeNew,completedTripSizeNew);
        assertThat(
                completedTripSizeNew
        ).isGreaterThan(completedTripSize);

        assertThat(
                inProgressTripSize
        ).isGreaterThan(inProgressTripSizeNew);
    }


}