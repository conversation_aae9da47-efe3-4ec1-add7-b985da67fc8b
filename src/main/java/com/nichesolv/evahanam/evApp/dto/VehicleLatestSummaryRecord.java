package com.nichesolv.evahanam.evApp.dto;

/**
 * Immutable record for Vehicle Latest Summary data.
 * Eliminates need for getters/setters and provides built-in equals, hashCode, toString.
 * More memory efficient and thread-safe compared to traditional DTOs.
 */
public record VehicleLatestSummaryRecord(
    String modelNo,
    String imei,
    Double latitude,
    Double longitude,
    Double distance,
    Integer count
) {
    
    // Static factory methods for different use cases
    public static VehicleLatestSummaryRecord forRunning(String imei, String modelNo, Double latitude, Double longitude, Double distance) {
        return new VehicleLatestSummaryRecord(modelNo, imei, latitude, longitude, distance, null);
    }
    
    public static VehicleLatestSummaryRecord forTrips(String imei, String modelNo, Double latitude, Double longitude, Integer count) {
        return new VehicleLatestSummaryRecord(modelNo, imei, latitude, longitude, null, count);
    }
    
    public static VehicleLatestSummaryRecord forAlerts(String imei, String modelNo, Double latitude, Double longitude, Integer count) {
        return new VehicleLatestSummaryRecord(modelNo, imei, latitude, longitude, null, count);
    }
    
    public static VehicleLatestSummaryRecord forAlarms(String imei, String modelNo, Double latitude, Double longitude, Integer count) {
        return new VehicleLatestSummaryRecord(modelNo, imei, latitude, longitude, null, count);
    }
}
