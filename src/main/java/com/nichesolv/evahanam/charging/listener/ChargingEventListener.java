package com.nichesolv.evahanam.charging.listener;

import com.nichesolv.evahanam.charging.jpa.ChargingEvent;
import com.nichesolv.evahanam.charging.service.ChargingEventService;
import com.nichesolv.evahanam.common.enums.EventType;
import com.nichesolv.evahanam.common.events.VehicleStateEvent;
import com.nichesolv.evahanam.common.util.EvMessageBundle;
import com.nichesolv.evahanam.trip.enums.EventConstants;
import com.nichesolv.evahanam.trip.jpa.EventDurationConstants;
import com.nichesolv.evahanam.trip.jpa.VehicleEventMonitor;
import com.nichesolv.evahanam.trip.repository.EventDurationConstantsRepository;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.jpa.VehicleStatus;
import com.nichesolv.evahanam.vehicleTests.exception.TripConstantNotFoundException;
import com.nichesolv.nds.model.organisation.CustomOrganisation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Optional;

@Slf4j
@Component
public class ChargingEventListener implements ApplicationListener<VehicleStateEvent> {

    @Autowired
    EventDurationConstantsRepository eventDurationConstantsRepository;

    @Autowired
    ChargingEventService chargingEventService;

    @Autowired
    EvMessageBundle evMessageBundle;

    @Override
    public void onApplicationEvent(VehicleStateEvent event) {
        // Logic to handle the charging event
        if (event == null || event.getEventMonitor() == null || event.getVehicleStatus() == null) {
            log.warn("Received null VehicleMonitorEvent, skipping trip processing.");
            return;
        }

        VehicleStatus vehicleStatus = event.getVehicleStatus();

        VehicleEventMonitor vehicleEventMonitor = event.getEventMonitor();

        Vehicle vehicle = vehicleEventMonitor.getVehicle();
        Long chargingTime = vehicleEventMonitor.getChargingTime();
        Long stoppageTime = vehicleEventMonitor.getStoppageTime()+ vehicleEventMonitor.getRunningTime();
        EventDurationConstants startChargeEventDurationConstant = eventDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.CHARGE_START_DURATION, EventType.CHARGING).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("CHARGE_CONSTANT_NOT_FOUND", EventConstants.CHARGE_START_DURATION.name(), vehicle.getManufacturer().getId())));
        EventDurationConstants stopChargeEventDurationConstant = eventDurationConstantsRepository.findByOrganisationAndConstantAndType((CustomOrganisation) vehicle.getManufacturer(), EventConstants.CHARGE_STOP_DURATION, EventType.CHARGING).orElseThrow(() -> new TripConstantNotFoundException(evMessageBundle.getMessage("CHARGE_CONSTANT_NOT_FOUND", EventConstants.CHARGE_STOP_DURATION.name(), vehicle.getManufacturer().getId())));
        Optional<ChargingEvent> chargingEventInProgress = chargingEventService.getLatestChargingEventByVehicle(vehicle);

        log.debug("Start::Charging event for vehicle {} with status {} ,inProgress:{}", vehicle.getImei(), vehicleStatus.getVehicleState(), chargingEventInProgress.isPresent());
        //saving or updating the trip
        if (chargingTime >= startChargeEventDurationConstant.getDuration() && chargingEventInProgress.isEmpty()) {
            // Start a new charging event
            /**
             * The start time for the charging event is set to the timestamp of the vehicle status minus the charging time minimum threshold.
             */
            Instant startTime = vehicleStatus.getVehicleStatusIdx().getTimestamp().minusSeconds(chargingTime);
            chargingEventService.startChargingEvent(vehicle, startTime);
        } else if (stoppageTime >= stopChargeEventDurationConstant.getDuration() && chargingEventInProgress.isPresent()) {
            ChargingEvent chargingEvent = chargingEventInProgress.get();
            /**
             * The end time for the charging event is set to the timestamp of the vehicle status minus the stoppage time maximum threshold.
             * This ensures that the charging event reflects the actual time when the vehicle stopped charging.
             */
            Instant endTime = vehicleStatus.getVehicleStatusIdx().getTimestamp().minusSeconds(stoppageTime);
            chargingEventService.stopChargingEvent(chargingEvent, endTime);
        }else{
            log.debug("No action required for vehicle {} under {} status", vehicle.getImei(), vehicleStatus.getVehicleState());
        }

    }
}
