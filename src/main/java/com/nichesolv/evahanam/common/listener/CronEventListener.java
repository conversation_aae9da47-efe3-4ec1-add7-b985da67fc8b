package com.nichesolv.evahanam.common.listener;

import com.nichesolv.evahanam.common.events.CronEvent;
import com.nichesolv.evahanam.common.jpa.ActiveVehicleSubscriptionPlan;
import com.nichesolv.evahanam.common.jpa.CronFrequency;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlan;
import com.nichesolv.evahanam.common.jpa.DataFrequencyPlanDetails;
import com.nichesolv.evahanam.common.repository.ActiveVehicleSubscriptionPlanRepository;
import com.nichesolv.evahanam.common.repository.CronFrequencyRepository;
import com.nichesolv.evahanam.telemetryData.repository.VehicleDataRepository;
import com.nichesolv.evahanam.vehicle.dto.CronDataSource;
import com.nichesolv.evahanam.vehicle.enums.OperationStatus;
import com.nichesolv.evahanam.vehicle.jpa.Vehicle;
import com.nichesolv.evahanam.vehicle.service.VehicleService;
import com.nichesolv.evahanam.trip.service.TripSummaryService;
import com.nichesolv.evahanam.vehicleTests.enums.TestStatus;
import com.nichesolv.evahanam.vehicleTests.jpa.VehicleTest;
import com.nichesolv.evahanam.vehicleTests.repository.VehicleTestRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Stream;

@Component
@Slf4j
public class CronEventListener implements ApplicationListener<CronEvent> {

    @Autowired
    CronFrequencyRepository cronFrequencyRepository;

    @Autowired
    ActiveVehicleSubscriptionPlanRepository activeVehicleSubscriptionPlanRepository;

    @Autowired
    VehicleService vehicleService;

    @Autowired
    TripSummaryService tripSummaryService;

    @Autowired
    VehicleDataRepository vehicleDataRepository;

    @Autowired
    private VehicleTestRepository vehicleTestRepository;

    @Override
    @Transactional
    public void onApplicationEvent(CronEvent event) {
        CronDataSource data = event.getSource();
        LocalDateTime currentLocalDataTime = data.getCurrentLocalTime();
        List<Integer> cronTimes = new ArrayList<>();
        cronTimes.add(currentLocalDataTime.getSecond());
        List<ChronoUnit> chronoUnits = new ArrayList<>();
        chronoUnits.add(ChronoUnit.SECONDS);
        if (currentLocalDataTime.getMinute() == 0 && currentLocalDataTime.getSecond() == 0) {
            cronTimes.add(currentLocalDataTime.getMinute());
            chronoUnits.add(ChronoUnit.MINUTES);
        }
        //checking if the time is 12:35 AM
        if(currentLocalDataTime.getHour() == 19 && currentLocalDataTime.getMinute() == 5 && currentLocalDataTime.getSecond() == 0){
            cronTimes.add(currentLocalDataTime.getHour());
            chronoUnits.add(ChronoUnit.HOURS);
        }
        if(currentLocalDataTime.getHour() == 12 && currentLocalDataTime.getMinute() == 30 && currentLocalDataTime.getSecond() ==0)
        {
            archiveOldTests();
        }
        List<CronFrequency> cronFrequencies = cronFrequencyRepository.findByCronTimeInAndUnitIn(cronTimes, chronoUnits);
        List<DataFrequencyPlanDetails> dataFrequencyPlanDetails = cronFrequencies.stream().flatMap(cronFrequency -> cronFrequency.getDataFrequencyPlanDetails().stream()).toList();
        dataFrequencyPlanDetails.forEach(e -> {

            try (Stream<String> vehicleImeiStream = getVehiclesByDataFrequencyPlan(e.getDataFrequencyPlan()).stream().map(Vehicle::getImei)) {
                vehicleImeiStream.forEach(imei -> {
                    switch (e.getFeatureName()) {
                        case VEHICLE_STATUS -> {
                            vehicleService.saveVehicleState(imei, data.getCurrentTime(), e);
                        }
                        case VEHICLE_STATUS_UPDATION -> {
                            vehicleService.updateVehicleStateForDataDelay(imei, data.getCurrentTime(), e);
                        }
                        case TRIP_UPDATION -> {
                            tripSummaryService.saveDataDelayedAutomaticTrips(imei, data.getCurrentTime(),e);
                        }
                    }
                });
            }
        });
    }

    /**
     * This method archives the tests which are older than the data present in the db.
     */
    @Transactional
    private void archiveOldTests() {
        log.info("Starting scheduled test archival process");
        try {
            Instant earliestTimestamp = vehicleDataRepository.findEarliestTimestamp();
            if (earliestTimestamp != null) {
                // Archive only the tests that are COMPLETED and created before the earliest timestamp
                List<VehicleTest> oldTests = vehicleTestRepository.findByCreatedOnBeforeAndStatus(earliestTimestamp, TestStatus.COMPLETED);
                if (!oldTests.isEmpty()) {
                    oldTests.forEach(test -> test.setStatus(TestStatus.ARCHIVED));
                    vehicleTestRepository.saveAll(oldTests);
                    log.info("Archived {} tests successfully", oldTests.size());
                } else {
                    log.info("No completed tests found for archival");
                }
            } else {
                log.warn("Could not determine earliest timestamp for archival");
            }
        } catch (Exception e) {
            log.error("Exception occured during test archival : {}", e.getMessage(), e);
        }
    }

    /**
     * This method finds the ACTIVE vehicles based on the parameter
     *
     * @param dataFrequencyPlan plan
     * @return list of ACTIVE vehicles which comes under that dataFrequencyPlan
     */
    public List<Vehicle> getVehiclesByDataFrequencyPlan(DataFrequencyPlan dataFrequencyPlan) {
        List<Vehicle> vehicles = new ArrayList<>();
        Predicate<ActiveVehicleSubscriptionPlan> isVehicleActiveCheck = activeVehicleSubscriptionPlan -> activeVehicleSubscriptionPlan.getVehicle().getOperationStatus().equals(OperationStatus.ACTIVE);
        try (Stream<ActiveVehicleSubscriptionPlan> stream = activeVehicleSubscriptionPlanRepository.findAllByDataFrequencyPlan(dataFrequencyPlan)) {
            vehicles = stream
                    .filter(isVehicleActiveCheck)
                    .map(ActiveVehicleSubscriptionPlan::getVehicle)
                    .toList();
        }
        return vehicles;
    }
}
