plugins {
    id 'java'
    id 'org.springframework.boot' version '3.1.1'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'maven-publish'
    id "io.freefair.lombok" version "8.1.0"
}

group = 'com.nichesolv.evahanam'
version = '0.1.2'

java {
    sourceCompatibility = '17'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    } 
}

repositories {
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
    maven { url 'https://repo.spring.io/snapshot' }
    maven {
        name = "GitHubPackages"
        url = "https://maven.pkg.github.com/Niche-soft/niche-user-be"
        url = "https://maven.pkg.github.com/Niche-Soft/nds-user-be"
        credentials {
            username = project.findProperty("gpr.user") ?: System.getenv("GITHUB_USERNAME")
            password = project.findProperty("gpr.key") ?: System.getenv("GITHUB_TOKEN")
        }
    }
}

dependencies {
    implementation 'com.auth0:java-jwt:4.3.0'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.6.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0'
    implementation 'org.springframework.boot:spring-boot-starter-validation:3.1.1'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'com.nichesolv:nds:0.0.0.7.6'
    implementation 'com.nichesolv:niche-user-be:0.0.0.7-RELEASE'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.12.5' // Use the appropriate version
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    implementation 'org.springframework:spring-webflux:6.0.11'
    testImplementation 'org.springframework.security:spring-security-test:6.1.2'
    implementation 'org.flywaydb:flyway-core'
    implementation 'com.github.alexmojaki:s3-stream-upload:2.2.4'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-csv:2.2.3'
    implementation 'com.google.firebase:firebase-admin:9.2.0'
    implementation 'com.amazonaws:aws-java-sdk-s3:1.11.705'
    implementation 'org.hibernate:hibernate-spatial:6.2.5.Final'
    runtimeOnly 'com.h2database:h2'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'io.projectreactor.netty:reactor-netty:1.1.10'

}

tasks.named('test') {
    useJUnitPlatform()
}

// Package publishing configuration.
publishing {
    repositories {
        maven {
            name = "GitHubPackages"
            url = uri("https://maven.pkg.github.com/niche-soft/nds-udp-models")
            credentials {
                username = project.findProperty("gpr.user") ?: System.getenv("GITHUB_USERNAME")
                password = project.findProperty("gpr.key") ?: System.getenv("GITHUB_TOKEN")
            }
        }
    }
    publications {
        gpr(MavenPublication) {
            from(components.java)
        }
    }
}
bootJar {
    archiveFileName = 'ev-be-server.jar'
}